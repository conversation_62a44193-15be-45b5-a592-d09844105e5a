/**
 * WooCommerce Configuration Component
 * Setup and manage WooCommerce API connection settings
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Eye, 
  EyeOff,
  Settings,
  Globe,
  Key,
  Clock
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

// Define the WooCommerceConfig interface locally to avoid import issues
interface WooCommerceConfig {
  id?: string;
  organization_id: string;
  store_url: string;
  consumer_key: string;
  consumer_secret: string;
  webhook_secret?: string;
  api_version?: string;
  sync_enabled?: boolean;
  batch_size?: number;
  sync_frequency_minutes?: number;
}

function WooCommerceConfiguration() {
  const [config, setConfig] = useState<WooCommerceConfig>({
    organization_id: 'mock-org-id', // Replace with actual from context
    store_url: '',
    consumer_key: '',
    consumer_secret: '',
    webhook_secret: '',
    api_version: 'v3',
    sync_enabled: true,
    batch_size: 100,
    sync_frequency_minutes: 15,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [showSecrets, setShowSecrets] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('woocommerce_config')
        .select('*')
        .eq('organization_id', config.organization_id)
        .single();

      if (data && !error) {
        setConfig(data);
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    setSaveMessage(null);
    
    try {
      const { error } = await supabase
        .from('woocommerce_config')
        .upsert({
          ...config,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'organization_id'
        });

      if (error) {
        throw error;
      }

      setSaveMessage('Configuration saved successfully!');
      setTimeout(() => setSaveMessage(null), 3000);
    } catch (error) {
      console.error('Failed to save configuration:', error);
      setSaveMessage('Failed to save configuration. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setTestResult(null);

    try {
      // Test connection directly
      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      const response = await fetch(`${baseUrl}/system_status`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setTestResult({ success: true, message: 'Connection successful! WooCommerce API is accessible.' });
      } else {
        setTestResult({ success: false, message: `Connection failed: HTTP ${response.status} - ${response.statusText}` });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleInputChange = (field: keyof WooCommerceConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value
    }));
    setTestResult(null); // Clear test result when config changes
  };

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const isConfigValid = () => {
    return (
      config.store_url &&
      validateUrl(config.store_url) &&
      config.consumer_key &&
      config.consumer_secret &&
      config.batch_size >= 10 &&
      config.batch_size <= 1000 &&
      config.sync_frequency_minutes >= 5
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">WooCommerce Configuration</h1>
          <p className="text-muted-foreground">
            Configure your WooCommerce store connection and sync settings
          </p>
        </div>
        <div className="flex items-center gap-2">
          {config.sync_enabled ? (
            <Badge variant="default" className="bg-green-500">
              <CheckCircle className="w-3 h-3 mr-1" />
              Sync Enabled
            </Badge>
          ) : (
            <Badge variant="secondary">
              <AlertCircle className="w-3 h-3 mr-1" />
              Sync Disabled
            </Badge>
          )}
        </div>
      </div>

      {/* API Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            Store Connection
          </CardTitle>
          <CardDescription>
            Configure your WooCommerce store API connection details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="store_url">Store URL</Label>
              <Input
                id="store_url"
                placeholder="https://yourstore.com"
                value={config.store_url}
                onChange={(e) => handleInputChange('store_url', e.target.value)}
                className={!validateUrl(config.store_url) && config.store_url ? 'border-red-500' : ''}
              />
              {!validateUrl(config.store_url) && config.store_url && (
                <p className="text-sm text-red-500">Please enter a valid URL</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="api_version">API Version</Label>
              <select
                id="api_version"
                value={config.api_version}
                onChange={(e) => handleInputChange('api_version', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="v3">v3 (Recommended)</option>
                <option value="v2">v2</option>
                <option value="v1">v1</option>
              </select>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>API Credentials</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSecrets(!showSecrets)}
              >
                {showSecrets ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                {showSecrets ? 'Hide' : 'Show'} Secrets
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="consumer_key">Consumer Key</Label>
                <Input
                  id="consumer_key"
                  type={showSecrets ? 'text' : 'password'}
                  placeholder="ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  value={config.consumer_key}
                  onChange={(e) => handleInputChange('consumer_key', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="consumer_secret">Consumer Secret</Label>
                <Input
                  id="consumer_secret"
                  type={showSecrets ? 'text' : 'password'}
                  placeholder="cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                  value={config.consumer_secret}
                  onChange={(e) => handleInputChange('consumer_secret', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="webhook_secret">Webhook Secret (Optional)</Label>
              <Input
                id="webhook_secret"
                type={showSecrets ? 'text' : 'password'}
                placeholder="webhook_secret_key"
                value={config.webhook_secret || ''}
                onChange={(e) => handleInputChange('webhook_secret', e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Used to verify webhook authenticity (recommended for production)
              </p>
            </div>
          </div>

          {/* Test Connection */}
          <div className="flex items-center gap-4 pt-4 border-t">
            <Button
              onClick={handleTestConnection}
              disabled={!isConfigValid() || isTesting}
              variant="outline"
              className="flex items-center gap-2"
            >
              <TestTube className={`w-4 h-4 ${isTesting ? 'animate-pulse' : ''}`} />
              {isTesting ? 'Testing...' : 'Test Connection'}
            </Button>

            {testResult && (
              <div className="flex items-center gap-2">
                {testResult.success ? (
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Connected
                  </Badge>
                ) : (
                  <Badge variant="destructive">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Failed
                  </Badge>
                )}
                <span className="text-sm text-muted-foreground">
                  {testResult.message}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Sync Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Synchronization Settings
          </CardTitle>
          <CardDescription>
            Configure how data is synchronized between WooCommerce and your system
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Enable Synchronization</Label>
              <p className="text-sm text-muted-foreground">
                Allow automatic data synchronization from WooCommerce
              </p>
            </div>
            <Switch
              checked={config.sync_enabled}
              onCheckedChange={(checked) => handleInputChange('sync_enabled', checked)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="batch_size">Batch Size</Label>
              <Input
                id="batch_size"
                type="number"
                min="10"
                max="1000"
                value={config.batch_size}
                onChange={(e) => handleInputChange('batch_size', parseInt(e.target.value))}
              />
              <p className="text-sm text-muted-foreground">
                Number of records to process per batch (10-1000)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sync_frequency">Sync Frequency (minutes)</Label>
              <Input
                id="sync_frequency"
                type="number"
                min="5"
                value={config.sync_frequency_minutes}
                onChange={(e) => handleInputChange('sync_frequency_minutes', parseInt(e.target.value))}
              />
              <p className="text-sm text-muted-foreground">
                How often to check for updates (minimum 5 minutes)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Configuration */}
      <div className="flex items-center justify-between">
        <div>
          {saveMessage && (
            <Alert className={saveMessage.includes('success') ? 'border-green-500' : 'border-red-500'}>
              <AlertDescription>{saveMessage}</AlertDescription>
            </Alert>
          )}
        </div>
        
        <Button
          onClick={handleSave}
          disabled={!isConfigValid() || isLoading}
          className="flex items-center gap-2"
        >
          <Save className="w-4 h-4" />
          {isLoading ? 'Saving...' : 'Save Configuration'}
        </Button>
      </div>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="w-5 h-5" />
            Setup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="prose prose-sm max-w-none">
            <h4>To get your WooCommerce API credentials:</h4>
            <ol className="list-decimal list-inside space-y-2">
              <li>Go to your WooCommerce admin dashboard</li>
              <li>Navigate to <strong>WooCommerce → Settings → Advanced → REST API</strong></li>
              <li>Click <strong>"Add Key"</strong></li>
              <li>Set permissions to <strong>"Read/Write"</strong></li>
              <li>Copy the Consumer Key and Consumer Secret</li>
              <li>Paste them into the fields above</li>
            </ol>
            
            <h4 className="mt-4">Important Notes:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Ensure your WooCommerce store has SSL enabled (HTTPS)</li>
              <li>API credentials are stored securely and encrypted</li>
              <li>Test the connection before enabling synchronization</li>
              <li>Monitor sync logs for any issues</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default WooCommerceConfiguration;
