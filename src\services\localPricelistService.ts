import { realSupplierData, type SupplierPriceData } from '@/data/realSupplierData';
import * as XLSX from 'xlsx';

export interface ProcessedPricelistData {
  id: string;
  supplier_id: string;
  supplier_name: string;
  file_name: string;
  upload_date: string;
  processed_date: string;
  total_products: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error_message?: string;
  products: SupplierPriceData[];
}

export interface PriceAnalytics {
  supplier_id: string;
  supplier_name: string;
  total_products: number;
  average_price: number;
  price_range: {
    min: number;
    max: number;
  };
  category_breakdown: Array<{
    category: string;
    count: number;
    avg_price: number;
  }>;
  discount_analysis: {
    avg_tier1: number;
    avg_tier2: number;
    avg_tier3: number;
  };
  lead_time_analysis: {
    avg_days: number;
    min_days: number;
    max_days: number;
  };
}

class LocalPricelistService {
  private storageKey = 'supplier_pricelists';
  private productsKey = 'supplier_products';

  constructor() {
    this.initializeWithSampleData();
  }

  private initializeWithSampleData(): void {
    if (!localStorage.getItem(this.storageKey)) {
      this.loadSampleData();
    }
  }

  // Parse multiple file formats
  async parseFile(file: File): Promise<SupplierPriceData[]> {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'xlsx':
      case 'xls':
        return this.parseExcelFile(file);
      case 'csv':
        return this.parseCSVFile(file);
      case 'json':
        return this.parseJSONFile(file);
      default:
        throw new Error(`Unsupported file format: ${extension}`);
    }
  }

  private async parseExcelFile(file: File): Promise<SupplierPriceData[]> {
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'buffer' });

    // Try to find the best sheet with data
    let bestSheet = null;
    let maxRows = 0;

    for (const sheetName of workbook.SheetNames) {
      const sheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

      if (jsonData.length > maxRows) {
        maxRows = jsonData.length;
        bestSheet = sheet;
      }
    }

    if (!bestSheet) {
      throw new Error('No valid data found in Excel file');
    }

    // Convert to JSON with intelligent header detection
    const jsonData = XLSX.utils.sheet_to_json(bestSheet, {
      raw: false,
      defval: '',
      blankrows: false
    });

    return this.normalizeData(jsonData);
  }

  private async parseCSVFile(file: File): Promise<SupplierPriceData[]> {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    
    const data = lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim());
      const obj: any = {};
      headers.forEach((header, index) => {
        obj[header] = values[index];
      });
      return obj;
    });

    return this.normalizeData(data);
  }

  private async parseJSONFile(file: File): Promise<SupplierPriceData[]> {
    const text = await file.text();
    const data = JSON.parse(text);
    return this.normalizeData(Array.isArray(data) ? data : [data]);
  }

  private normalizeData(rawData: any[]): SupplierPriceData[] {
    return rawData.map((item, index) => {
      // Intelligent field mapping with multiple possible field names
      const mappedItem = this.intelligentFieldMapping(item, index);
      return mappedItem;
    });
  }

  private intelligentFieldMapping(item: any, index: number): SupplierPriceData {
    // Smart field detection patterns
    const fieldMappings = {
      supplier_name: [
        'supplier_name', 'supplier', 'vendor', 'company', 'supplier name', 'vendor name',
        'company name', 'business name', 'organization', 'firm'
      ],
      product_code: [
        'product_code', 'sku', 'item_code', 'part_number', 'model', 'product id',
        'item id', 'code', 'reference', 'part no', 'model no'
      ],
      product_name: [
        'product_name', 'name', 'title', 'description', 'item', 'product',
        'item name', 'product title', 'item description'
      ],
      unit_price: [
        'unit_price', 'price', 'cost', 'amount', 'rate', 'unit cost',
        'selling price', 'list price', 'retail price', 'wholesale price'
      ],
      category: [
        'category', 'type', 'group', 'classification', 'department',
        'product type', 'item type', 'product category'
      ],
      brand: [
        'brand', 'manufacturer', 'make', 'producer', 'vendor brand', 'brand name'
      ],
      currency: [
        'currency', 'curr', 'money', 'denomination'
      ],
      minimum_order_qty: [
        'minimum_order_qty', 'min_qty', 'moq', 'min order', 'minimum quantity',
        'min_order_quantity', 'minimum order quantity'
      ],
      lead_time_days: [
        'lead_time_days', 'lead_time', 'delivery_time', 'shipping_time',
        'lead time', 'delivery days', 'processing time'
      ]
    };

    // Auto-detect supplier name from filename or first valid entry
    const detectedSupplier = this.detectSupplierName(item);

    // Auto-detect category from product name
    const detectedCategory = this.detectCategory(this.findFieldValue(item, fieldMappings.product_name));

    return {
      id: item.id || `IMPORT_${Date.now()}_${index}`,
      supplier_name: this.findFieldValue(item, fieldMappings.supplier_name) || detectedSupplier || 'Unknown Supplier',
      company_code: item.company_code || item['Company Code'] || this.generateCompanyCode(detectedSupplier),
      contact_email: item.contact_email || item['Contact Email'] || '',
      product_code: this.findFieldValue(item, fieldMappings.product_code) || `PROD_${Date.now()}_${index}`,
      product_name: this.findFieldValue(item, fieldMappings.product_name) || 'Unknown Product',
      category: this.findFieldValue(item, fieldMappings.category) || detectedCategory || 'General',
      brand: this.findFieldValue(item, fieldMappings.brand) || this.detectBrand(this.findFieldValue(item, fieldMappings.product_name)) || 'Generic',
      unit_price: this.parsePrice(this.findFieldValue(item, fieldMappings.unit_price)),
      currency: this.findFieldValue(item, fieldMappings.currency) || 'USD',
      minimum_order_qty: this.parseInteger(this.findFieldValue(item, fieldMappings.minimum_order_qty)) || 1,
      lead_time_days: this.parseInteger(this.findFieldValue(item, fieldMappings.lead_time_days)) || 7,
      discount_tier_1: parseFloat(item.discount_tier_1 || item['Discount Tier 1'] || '0'),
      discount_tier_2: parseFloat(item.discount_tier_2 || item['Discount Tier 2'] || '0'),
      discount_tier_3: parseFloat(item.discount_tier_3 || item['Discount Tier 3'] || '0'),
      last_updated: item.last_updated || item['Last Updated'] || new Date().toISOString().split('T')[0],
      valid_until: item.valid_until || item['Valid Until'] || new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      region: item.region || item['Region'] || 'Global',
      terms: item.terms || item['Terms'] || 'NET30'
    };
  }

  private findFieldValue(item: any, possibleFields: string[]): string | undefined {
    for (const field of possibleFields) {
      // Check exact match (case insensitive)
      const exactMatch = Object.keys(item).find(key =>
        key.toLowerCase() === field.toLowerCase()
      );
      if (exactMatch && item[exactMatch]) {
        return String(item[exactMatch]).trim();
      }

      // Check partial match
      const partialMatch = Object.keys(item).find(key =>
        key.toLowerCase().includes(field.toLowerCase()) ||
        field.toLowerCase().includes(key.toLowerCase())
      );
      if (partialMatch && item[partialMatch]) {
        return String(item[partialMatch]).trim();
      }
    }
    return undefined;
  }

  private detectSupplierName(item: any): string {
    // Try to detect supplier from various fields
    const possibleSupplierFields = Object.keys(item).filter(key =>
      key.toLowerCase().includes('supplier') ||
      key.toLowerCase().includes('vendor') ||
      key.toLowerCase().includes('company')
    );

    for (const field of possibleSupplierFields) {
      if (item[field] && String(item[field]).trim()) {
        return String(item[field]).trim();
      }
    }

    return 'Unknown Supplier';
  }

  private detectCategory(productName?: string): string {
    if (!productName) return 'General';

    const categoryKeywords = {
      // Audio & Sound
      'Audio Equipment': [
        'microphone', 'mic', 'speaker', 'amplifier', 'amp', 'mixer', 'audio', 'sound',
        'headphone', 'earphone', 'subwoofer', 'tweeter', 'woofer', 'pa system', 'soundboard',
        'preamp', 'compressor', 'equalizer', 'reverb', 'delay', 'effects', 'vocal', 'instrument'
      ],

      // Lighting & Effects
      'Lighting Equipment': [
        'light', 'led', 'par', 'spot', 'beam', 'fixture', 'lamp', 'strobe', 'wash', 'flood',
        'moving head', 'scanner', 'gobo', 'fresnel', 'ellipsoidal', 'cyc', 'strip', 'bar',
        'rgb', 'rgbw', 'cob', 'pixel', 'matrix', 'panel', 'tube', 'bulb'
      ],

      'Lighting Control': [
        'dmx', 'controller', 'console', 'desk', 'lighting desk', 'dimmer', 'relay', 'switch pack',
        'artnet', 'sacn', 'rdm', 'wireless dmx', 'lighting control', 'programmer'
      ],

      'Atmospheric Effects': [
        'fog', 'haze', 'smoke', 'bubble', 'snow', 'confetti', 'pyro', 'flame', 'co2', 'dry ice',
        'atmospheric', 'effect machine', 'generator', 'fluid', 'juice'
      ],

      // Video & Display
      'Video Equipment': [
        'camera', 'monitor', 'screen', 'projector', 'video', 'display', 'lcd', 'led screen',
        'plasma', 'oled', 'tv', 'television', 'webcam', 'camcorder', 'lens', 'tripod',
        'video mixer', 'switcher', 'scaler', 'converter', 'capture', 'recorder'
      ],

      'Projection & Screens': [
        'projector', 'screen', 'projection', 'backdrop', 'scrim', 'cyclorama', 'rear projection',
        'front projection', 'mapping', 'blend', 'edge blend', 'warp', 'keystone'
      ],

      // Connectivity & Infrastructure
      'Cables & Accessories': [
        'cable', 'cord', 'connector', 'adapter', 'wire', 'xlr', 'trs', 'rca', 'bnc', 'hdmi',
        'usb', 'ethernet', 'cat5', 'cat6', 'fiber', 'optical', 'coax', 'patch', 'extension',
        'splitter', 'combiner', 'di box', 'direct box', 'snake', 'multicore'
      ],

      'Power & Electrical': [
        'power', 'battery', 'charger', 'electrical', 'voltage', 'ups', 'pdu', 'distribution',
        'outlet', 'socket', 'plug', 'extension', 'generator', 'inverter', 'transformer',
        'breaker', 'fuse', 'surge protector', 'power supply', 'psu'
      ],

      // Computing & Technology
      'Computing Hardware': [
        'laptop', 'computer', 'server', 'processor', 'cpu', 'memory', 'ram', 'storage', 'ssd',
        'hard drive', 'motherboard', 'graphics card', 'gpu', 'workstation', 'desktop', 'tablet',
        'ipad', 'macbook', 'pc', 'tower', 'rack mount', 'blade server'
      ],

      'Networking': [
        'router', 'switch', 'firewall', 'network', 'ethernet', 'wifi', 'wireless', 'access point',
        'modem', 'gateway', 'bridge', 'repeater', 'hub', 'nas', 'storage', 'vpn', 'security'
      ],

      'Software & Licenses': [
        'software', 'license', 'subscription', 'app', 'application', 'program', 'suite',
        'operating system', 'windows', 'macos', 'linux', 'office', 'adobe', 'autodesk'
      ],

      // Production & Staging
      'Staging & Rigging': [
        'stage', 'platform', 'deck', 'riser', 'truss', 'rigging', 'hoist', 'motor', 'chain',
        'wire rope', 'shackle', 'clamp', 'coupler', 'scaffold', 'barrier', 'fence', 'rail'
      ],

      'Draping & Fabric': [
        'drape', 'curtain', 'fabric', 'backdrop', 'scrim', 'gauze', 'velour', 'silk', 'muslin',
        'cyc', 'cyclorama', 'border', 'leg', 'traveler', 'track', 'pipe', 'batten'
      ],

      // Tools & Equipment
      'Tools & Equipment': [
        'tool', 'equipment', 'instrument', 'device', 'meter', 'tester', 'analyzer', 'scope',
        'multimeter', 'oscilloscope', 'generator', 'calibrator', 'wrench', 'screwdriver',
        'drill', 'saw', 'hammer', 'pliers', 'crimper', 'stripper', 'soldering'
      ],

      'Cases & Transport': [
        'case', 'flight case', 'road case', 'rack case', 'transport', 'shipping', 'crate',
        'box', 'container', 'bag', 'pouch', 'cover', 'protection', 'foam', 'padding'
      ],

      // Specialized Categories
      'Security & Safety': [
        'security', 'safety', 'alarm', 'surveillance', 'cctv', 'camera security', 'sensor',
        'detector', 'emergency', 'exit', 'fire', 'smoke detector', 'sprinkler', 'extinguisher'
      ],

      'Communication': [
        'intercom', 'walkie talkie', 'radio', 'headset', 'microphone wireless', 'transmitter',
        'receiver', 'antenna', 'communication', 'paging', 'announcement', 'pa system'
      ],

      'Furniture & Seating': [
        'chair', 'table', 'desk', 'furniture', 'seating', 'bench', 'stool', 'podium', 'lectern',
        'stand', 'rack', 'cabinet', 'shelf', 'storage', 'locker', 'wardrobe'
      ]
    };

    const productLower = productName.toLowerCase();

    // Score-based matching for better accuracy
    let bestMatch = 'General';
    let bestScore = 0;

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      let score = 0;
      for (const keyword of keywords) {
        if (productLower.includes(keyword)) {
          // Longer keywords get higher scores
          score += keyword.length;
          // Exact word matches get bonus points
          if (productLower.split(' ').includes(keyword)) {
            score += 5;
          }
        }
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = category;
      }
    }

    return bestMatch;
  }

  private detectBrand(productName?: string): string {
    if (!productName) return 'Generic';

    const brandDatabase = {
      // Audio Brands
      'Shure': ['shure', 'sm58', 'sm57', 'beta'],
      'Sennheiser': ['sennheiser', 'e835', 'e945', 'mkh'],
      'Audio-Technica': ['audio-technica', 'at2020', 'at4040'],
      'QSC': ['qsc', 'k12', 'k10', 'kw'],
      'Yamaha': ['yamaha', 'mg', 'tf', 'cl'],
      'Behringer': ['behringer', 'x32', 'wing'],
      'Mackie': ['mackie', 'onyx', 'profx'],
      'JBL': ['jbl', 'srx', 'prx', 'vtx'],
      'Electro-Voice': ['electro-voice', 'ev', 're20', 'nd'],
      'Rode': ['rode', 'procaster', 'podcaster'],

      // Lighting Brands
      'Chauvet': ['chauvet', 'rogue', 'colorado', 'maverick'],
      'Martin': ['martin', 'mac', 'atomic', 'rush'],
      'Elation': ['elation', 'artisan', 'proteus', 'platinum'],
      'ADJ': ['adj', 'american dj', 'mega'],
      'Avolites': ['avolites', 'sapphire', 'tiger', 'quartz'],
      'ETC': ['etc', 'source four', 'ion', 'eos'],
      'Clay Paky': ['clay paky', 'sharpy', 'mythos'],
      'Robe': ['robe', 'pointe', 'bmfl', 'robin'],
      'GrandMA': ['grandma', 'ma lighting', 'ma2', 'ma3'],
      'Hog': ['hog', 'high end', 'road hog'],

      // Video/Display Brands
      'Sony': ['sony', 'fx', 'a7', 'pxw'],
      'Canon': ['canon', 'eos', 'xf', 'c300'],
      'Panasonic': ['panasonic', 'ag', 'au', 'pt'],
      'Blackmagic': ['blackmagic', 'ursa', 'pocket', 'atem'],
      'Samsung': ['samsung', 'galaxy', 'qled'],
      'LG': ['lg', 'oled', 'nanocell'],
      'Epson': ['epson', 'powerlite', 'pro'],
      'Barco': ['barco', 'flm', 'hdx', 'e2'],
      'Christie': ['christie', 'roadster', 'boxer'],

      // Computing Brands
      'Apple': ['apple', 'macbook', 'imac', 'ipad', 'iphone'],
      'Dell': ['dell', 'latitude', 'precision', 'optiplex'],
      'HP': ['hp', 'elitebook', 'pavilion', 'omen'],
      'Lenovo': ['lenovo', 'thinkpad', 'ideapad'],
      'Microsoft': ['microsoft', 'surface', 'xbox'],
      'Intel': ['intel', 'core', 'xeon', 'pentium'],
      'AMD': ['amd', 'ryzen', 'threadripper'],
      'NVIDIA': ['nvidia', 'geforce', 'quadro', 'tesla'],

      // Networking Brands
      'Cisco': ['cisco', 'catalyst', 'nexus', 'meraki'],
      'Netgear': ['netgear', 'nighthawk', 'orbi'],
      'Ubiquiti': ['ubiquiti', 'unifi', 'edgemax'],
      'TP-Link': ['tp-link', 'archer', 'deco'],
      'Linksys': ['linksys', 'velop', 'wrt'],

      // Power & Electrical
      'APC': ['apc', 'smart-ups', 'back-ups'],
      'Eaton': ['eaton', 'powerware', '9px'],
      'Furman': ['furman', 'power conditioner'],
      'Middle Atlantic': ['middle atlantic', 'racklink'],

      // Professional Equipment
      'Blackmagic Design': ['blackmagic design', 'davinci'],
      'Avid': ['avid', 'pro tools', 'media composer'],
      'NewTek': ['newtek', 'tricaster', 'talk show'],
      'Ross Video': ['ross video', 'carbonite', 'xpression'],
      'For-A': ['for-a', 'hanabi', 'fa'],

      // Cases & Transport
      'Pelican': ['pelican', 'storm', 'protector'],
      'SKB': ['skb', 'roto', 'mil-std'],
      'Gator': ['gator', 'g-tour', 'grc'],
      'Odyssey': ['odyssey', 'flight zone', 'krom']
    };

    const productLower = productName.toLowerCase();

    // Score-based brand detection
    let bestBrand = 'Generic';
    let bestScore = 0;

    for (const [brand, keywords] of Object.entries(brandDatabase)) {
      for (const keyword of keywords) {
        if (productLower.includes(keyword)) {
          const score = keyword.length + (productLower.startsWith(keyword) ? 5 : 0);
          if (score > bestScore) {
            bestScore = score;
            bestBrand = brand;
          }
        }
      }
    }

    return bestBrand;
  }

  private generateCompanyCode(supplierName?: string): string {
    if (!supplierName) return 'UNK';

    return supplierName
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .join('')
      .substring(0, 5);
  }

  private parsePrice(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      // Remove currency symbols and parse
      const cleaned = value.replace(/[$£€¥,]/g, '').trim();
      const parsed = parseFloat(cleaned);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  private parseInteger(value: any): number {
    if (typeof value === 'number') return Math.floor(value);
    if (typeof value === 'string') {
      const parsed = parseInt(value.replace(/[^0-9]/g, ''));
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  // Enhanced processing with validation and error handling
  async processUpload(file: File, supplierId?: string): Promise<ProcessedPricelistData> {
    const uploadId = `upload_${Date.now()}`;
    
    try {
      // Parse file
      const products = await this.parseFile(file);
      
      if (products.length === 0) {
        throw new Error('No valid products found in file');
      }

      // Validate data
      const validationErrors = this.validateProducts(products);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Store in localStorage
      const processedData: ProcessedPricelistData = {
        id: uploadId,
        supplier_id: supplierId || products[0].supplier_name.replace(/\s+/g, '_').toLowerCase(),
        supplier_name: products[0].supplier_name,
        file_name: file.name,
        upload_date: new Date().toISOString(),
        processed_date: new Date().toISOString(),
        total_products: products.length,
        status: 'completed',
        products
      };

      // Save to localStorage
      this.savePricelistData(processedData);
      
      return processedData;
    } catch (error) {
      return {
        id: uploadId,
        supplier_id: supplierId || 'unknown',
        supplier_name: 'Unknown',
        file_name: file.name,
        upload_date: new Date().toISOString(),
        processed_date: new Date().toISOString(),
        total_products: 0,
        status: 'error',
        error_message: error instanceof Error ? error.message : 'Unknown error',
        products: []
      };
    }
  }

  private validateProducts(products: SupplierPriceData[]): string[] {
    const errors: string[] = [];
    
    products.forEach((product, index) => {
      if (!product.product_name || product.product_name.trim() === '') {
        errors.push(`Row ${index + 1}: Product name is required`);
      }
      
      if (product.unit_price <= 0) {
        errors.push(`Row ${index + 1}: Unit price must be greater than 0`);
      }
      
      if (product.minimum_order_qty < 1) {
        errors.push(`Row ${index + 1}: Minimum order quantity must be at least 1`);
      }
    });
    
    return errors;
  }

  private savePricelistData(data: ProcessedPricelistData): void {
    // Get existing pricelists
    const existingPricelists = this.getAllPricelists();
    
    // Add new pricelist
    const updatedPricelists = [...existingPricelists, data];
    
    // Save to localStorage
    localStorage.setItem(this.storageKey, JSON.stringify(updatedPricelists));
    
    // Save products separately for easier querying
    const existingProducts = this.getAllProducts();
    const updatedProducts = [
      ...existingProducts,
      ...data.products.map(product => ({
        ...product,
        pricelist_id: data.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    ];
    
    localStorage.setItem(this.productsKey, JSON.stringify(updatedProducts));
  }

  // Get analytics for a specific supplier
  getSupplierAnalytics(supplierId: string): PriceAnalytics | null {
    const products = this.getAllProducts().filter(p => 
      p.supplier_name.replace(/\s+/g, '_').toLowerCase() === supplierId
    );

    if (products.length === 0) {
      return null;
    }

    const supplier = products[0];
    const prices = products.map(p => p.unit_price);
    const categories = products.reduce((acc, p) => {
      if (!acc[p.category]) {
        acc[p.category] = { count: 0, total_price: 0 };
      }
      acc[p.category].count++;
      acc[p.category].total_price += p.unit_price;
      return acc;
    }, {} as Record<string, { count: number; total_price: number }>);

    return {
      supplier_id: supplierId,
      supplier_name: supplier.supplier_name,
      total_products: products.length,
      average_price: prices.reduce((a, b) => a + b, 0) / prices.length,
      price_range: {
        min: Math.min(...prices),
        max: Math.max(...prices)
      },
      category_breakdown: Object.entries(categories).map(([category, data]) => ({
        category,
        count: data.count,
        avg_price: data.total_price / data.count
      })),
      discount_analysis: {
        avg_tier1: products.reduce((a, p) => a + p.discount_tier_1, 0) / products.length,
        avg_tier2: products.reduce((a, p) => a + p.discount_tier_2, 0) / products.length,
        avg_tier3: products.reduce((a, p) => a + p.discount_tier_3, 0) / products.length
      },
      lead_time_analysis: {
        avg_days: products.reduce((a, p) => a + p.lead_time_days, 0) / products.length,
        min_days: Math.min(...products.map(p => p.lead_time_days)),
        max_days: Math.max(...products.map(p => p.lead_time_days))
      }
    };
  }

  // Get all supplier pricelists
  getAllPricelists(): ProcessedPricelistData[] {
    const stored = localStorage.getItem(this.storageKey);
    return stored ? JSON.parse(stored) : [];
  }

  // Get all products
  getAllProducts(): SupplierPriceData[] {
    const stored = localStorage.getItem(this.productsKey);
    return stored ? JSON.parse(stored) : [];
  }

  // Search products across all suppliers
  searchProducts(query: string, filters?: {
    supplier_id?: string;
    category?: string;
    price_min?: number;
    price_max?: number;
  }): SupplierPriceData[] {
    let products = this.getAllProducts();

    // Filter by search query
    if (query.trim()) {
      products = products.filter(p => 
        p.product_name.toLowerCase().includes(query.toLowerCase()) ||
        p.product_code.toLowerCase().includes(query.toLowerCase()) ||
        p.brand.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Apply filters
    if (filters?.supplier_id) {
      products = products.filter(p => 
        p.supplier_name.replace(/\s+/g, '_').toLowerCase() === filters.supplier_id
      );
    }

    if (filters?.category) {
      products = products.filter(p => p.category === filters.category);
    }

    if (filters?.price_min) {
      products = products.filter(p => p.unit_price >= filters.price_min!);
    }

    if (filters?.price_max) {
      products = products.filter(p => p.unit_price <= filters.price_max!);
    }

    return products.slice(0, 100); // Limit results
  }

  // Load sample data for demo purposes
  loadSampleData(): void {
    const sampleUpload: ProcessedPricelistData = {
      id: 'sample_upload_2025',
      supplier_id: 'sample_suppliers',
      supplier_name: 'Mixed Suppliers Sample',
      file_name: 'sample_supplier_data.json',
      upload_date: new Date().toISOString(),
      processed_date: new Date().toISOString(),
      total_products: realSupplierData.length,
      status: 'completed',
      products: realSupplierData
    };

    this.savePricelistData(sampleUpload);
  }

  // Calculate price comparison between suppliers
  comparePrices(productName: string): Array<{
    supplier_name: string;
    product_name: string;
    unit_price: number;
    currency: string;
    lead_time_days: number;
    minimum_order_qty: number;
  }> {
    const products = this.getAllProducts().filter(p =>
      p.product_name.toLowerCase().includes(productName.toLowerCase())
    );

    return products
      .map(p => ({
        supplier_name: p.supplier_name,
        product_name: p.product_name,
        unit_price: p.unit_price,
        currency: p.currency,
        lead_time_days: p.lead_time_days,
        minimum_order_qty: p.minimum_order_qty
      }))
      .sort((a, b) => a.unit_price - b.unit_price);
  }

  // Get comprehensive analytics for all suppliers
  getComprehensiveAnalytics(): {
    totalSuppliers: number;
    totalProducts: number;
    averagePrice: number;
    topCategories: Array<{ category: string; count: number; percentage: number }>;
    priceDistribution: { low: number; medium: number; high: number };
    regionDistribution: Record<string, number>;
    leadTimeAnalysis: { avg: number; min: number; max: number };
  } {
    const products = this.getAllProducts();
    const suppliers = new Set(products.map(p => p.supplier_name));
    
    const prices = products.map(p => p.unit_price);
    const categories = products.reduce((acc, p) => {
      acc[p.category] = (acc[p.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const regions = products.reduce((acc, p) => {
      acc[p.region] = (acc[p.region] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const leadTimes = products.map(p => p.lead_time_days);

    const topCategories = Object.entries(categories)
      .map(([category, count]) => ({
        category,
        count,
        percentage: Math.round((count / products.length) * 100)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalSuppliers: suppliers.size,
      totalProducts: products.length,
      averagePrice: prices.reduce((a, b) => a + b, 0) / prices.length,
      topCategories,
      priceDistribution: {
        low: prices.filter(p => p <= 100).length,
        medium: prices.filter(p => p > 100 && p <= 1000).length,
        high: prices.filter(p => p > 1000).length
      },
      regionDistribution: regions,
      leadTimeAnalysis: {
        avg: leadTimes.reduce((a, b) => a + b, 0) / leadTimes.length,
        min: Math.min(...leadTimes),
        max: Math.max(...leadTimes)
      }
    };
  }

  // Clear all data
  clearAllData(): void {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.productsKey);
  }

  // Get supplier list
  getSupplierList(): Array<{ id: string; name: string; productCount: number }> {
    const products = this.getAllProducts();
    const supplierMap = products.reduce((acc, p) => {
      const id = p.supplier_name.replace(/\s+/g, '_').toLowerCase();
      if (!acc[id]) {
        acc[id] = { id, name: p.supplier_name, productCount: 0 };
      }
      acc[id].productCount++;
      return acc;
    }, {} as Record<string, { id: string; name: string; productCount: number }>);

    return Object.values(supplierMap);
  }
}

export const localPricelistService = new LocalPricelistService();
