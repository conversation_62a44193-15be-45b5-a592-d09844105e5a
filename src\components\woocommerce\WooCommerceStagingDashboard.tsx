/**
 * WooCommerce Staging Dashboard
 * Real-time data synchronization and validation interface
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Database,
  Users,
  Package,
  Trash2,
  Settings,
  Play,
  Pause
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

// Define interfaces locally to avoid import issues
interface SyncResult {
  success: boolean;
  records_processed: number;
  records_success: number;
  records_error: number;
  records_skipped: number;
  errors: string[];
  sync_log_id?: string;
}

interface StagingStats {
  customers: { total: number; pending: number; synced: number; error: number };
  products: { total: number; pending: number; synced: number; error: number };
  lastSync: string | null;
}

function WooCommerceStagingDashboard() {
  const [stats, setStats] = useState<StagingStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [syncInProgress, setSyncInProgress] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'error'>('unknown');
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);
  const [batchSize, setBatchSize] = useState(100);

  const [organizationId, setOrganizationId] = useState<string>('');

  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (user && !error) {
        setOrganizationId(user.id);
        await initializeAndLoadStats(user.id);
      }
    } catch (error) {
      console.error('Failed to get user:', error);
    }
  };

  const initializeAndLoadStats = async (orgId: string) => {
    setIsLoading(true);
    try {
      // Check if WooCommerce config exists and test connection
      const { data: config, error } = await supabase
        .from('woocommerce_config')
        .select('*')
        .eq('organization_id', orgId)
        .single();

      if (config && !error) {
        // Test real API connection
        const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
        const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

        try {
          const response = await fetch(`${baseUrl}/system_status`, {
            headers: {
              'Authorization': `Basic ${auth}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            setConnectionStatus('connected');
          } else {
            setConnectionStatus('error');
          }
        } catch (apiError) {
          console.error('API connection test failed:', apiError);
          setConnectionStatus('error');
        }

        await loadStats();
      } else {
        setConnectionStatus('error');
        setStats({
          customers: { total: 0, pending: 0, synced: 0, error: 0 },
          products: { total: 0, pending: 0, synced: 0, error: 0 },
          lastSync: null
        });
      }
    } catch (error) {
      console.error('Failed to initialize:', error);
      setConnectionStatus('error');
      setStats({
        customers: { total: 0, pending: 0, synced: 0, error: 0 },
        products: { total: 0, pending: 0, synced: 0, error: 0 },
        lastSync: null
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncCustomers = async () => {
    setSyncInProgress(true);
    try {
      // Get WooCommerce config
      const { data: config, error } = await supabase
        .from('woocommerce_config')
        .select('*')
        .eq('organization_id', organizationId)
        .single();

      if (!config || error) {
        throw new Error('WooCommerce configuration not found');
      }

      // Real API call to fetch customers
      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      const response = await fetch(`${baseUrl}/customers?per_page=${batchSize}`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${response.statusText}`);
      }

      const customers = await response.json();

      // Insert customers into staging
      let success = 0;
      let errors: string[] = [];

      for (const customer of customers) {
        try {
          const { error: insertError } = await supabase
            .from('woocommerce_customers_staging')
            .upsert({
              wc_customer_id: customer.id,
              organization_id: organizationId,
              email: customer.email,
              first_name: customer.first_name,
              last_name: customer.last_name,
              billing_address_1: customer.billing?.address_1,
              billing_city: customer.billing?.city,
              billing_country: customer.billing?.country,
              raw_data: customer,
              sync_status: 'pending',
              updated_at: new Date().toISOString(),
            }, {
              onConflict: 'organization_id,wc_customer_id'
            });

          if (insertError) {
            errors.push(`Customer ${customer.id}: ${insertError.message}`);
          } else {
            success++;
          }
        } catch (err) {
          errors.push(`Customer ${customer.id}: ${err}`);
        }
      }

      const result: SyncResult = {
        success: errors.length === 0,
        records_processed: customers.length,
        records_success: success,
        records_error: errors.length,
        records_skipped: 0,
        errors: errors,
      };

      setLastSyncResult(result);
      await loadStats();
    } catch (error) {
      console.error('Sync failed:', error);
      setLastSyncResult({
        success: false,
        records_processed: 0,
        records_success: 0,
        records_error: 1,
        records_skipped: 0,
        errors: [`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      });
    } finally {
      setSyncInProgress(false);
    }
  };

  const handleSyncProducts = async () => {
    setSyncInProgress(true);
    try {
      // Get WooCommerce config
      const { data: config, error } = await supabase
        .from('woocommerce_config')
        .select('*')
        .eq('organization_id', organizationId)
        .single();

      if (!config || error) {
        throw new Error('WooCommerce configuration not found');
      }

      // Real API call to fetch products
      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      const response = await fetch(`${baseUrl}/products?per_page=${batchSize}`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${response.statusText}`);
      }

      const products = await response.json();

      // Insert products into staging
      let success = 0;
      let errors: string[] = [];

      for (const product of products) {
        try {
          const { error: insertError } = await supabase
            .from('woocommerce_products_staging')
            .upsert({
              wc_product_id: product.id,
              organization_id: organizationId,
              name: product.name,
              sku: product.sku,
              price: parseFloat(product.price || '0'),
              currency: 'ZAR', // Default to ZAR
              status: product.status,
              stock_quantity: product.stock_quantity,
              raw_data: product,
              sync_status: 'pending',
              updated_at: new Date().toISOString(),
            }, {
              onConflict: 'organization_id,wc_product_id'
            });

          if (insertError) {
            errors.push(`Product ${product.id}: ${insertError.message}`);
          } else {
            success++;
          }
        } catch (err) {
          errors.push(`Product ${product.id}: ${err}`);
        }
      }

      const result: SyncResult = {
        success: errors.length === 0,
        records_processed: products.length,
        records_success: success,
        records_error: errors.length,
        records_skipped: 0,
        errors: errors,
      };

      setLastSyncResult(result);
      await loadStats();
    } catch (error) {
      console.error('Sync failed:', error);
      setLastSyncResult({
        success: false,
        records_processed: 0,
        records_success: 0,
        records_error: 1,
        records_skipped: 0,
        errors: [`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      });
    } finally {
      setSyncInProgress(false);
    }
  };

  const handleFullSync = async () => {
    setSyncInProgress(true);
    try {
      // Run both customer and product sync in parallel
      const [customersResult, productsResult] = await Promise.all([
        handleSyncCustomersInternal(),
        handleSyncProductsInternal()
      ]);

      const combinedResult: SyncResult = {
        success: customersResult.success && productsResult.success,
        records_processed: customersResult.records_processed + productsResult.records_processed,
        records_success: customersResult.records_success + productsResult.records_success,
        records_error: customersResult.records_error + productsResult.records_error,
        records_skipped: customersResult.records_skipped + productsResult.records_skipped,
        errors: [...customersResult.errors, ...productsResult.errors],
      };

      setLastSyncResult(combinedResult);
      await loadStats();
    } catch (error) {
      console.error('Full sync failed:', error);
      setLastSyncResult({
        success: false,
        records_processed: 0,
        records_success: 0,
        records_error: 1,
        records_skipped: 0,
        errors: [`Full sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
      });
    } finally {
      setSyncInProgress(false);
    }
  };

  // Internal sync functions that return results without setting state
  const handleSyncCustomersInternal = async (): Promise<SyncResult> => {
    const { data: config, error } = await supabase
      .from('woocommerce_config')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (!config || error) {
      throw new Error('WooCommerce configuration not found');
    }

    const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
    const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

    const response = await fetch(`${baseUrl}/customers?per_page=${batchSize}`, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Customers API Error: ${response.status} - ${response.statusText}`);
    }

    const customers = await response.json();
    let success = 0;
    let errors: string[] = [];

    for (const customer of customers) {
      try {
        const { error: insertError } = await supabase
          .from('woocommerce_customers_staging')
          .upsert({
            wc_customer_id: customer.id,
            organization_id: organizationId,
            email: customer.email,
            first_name: customer.first_name,
            last_name: customer.last_name,
            billing_address_1: customer.billing?.address_1,
            billing_city: customer.billing?.city,
            billing_country: customer.billing?.country,
            raw_data: customer,
            sync_status: 'pending',
            updated_at: new Date().toISOString(),
          }, {
            onConflict: 'organization_id,wc_customer_id'
          });

        if (!insertError) success++;
        else errors.push(`Customer ${customer.id}: ${insertError.message}`);
      } catch (err) {
        errors.push(`Customer ${customer.id}: ${err}`);
      }
    }

    return {
      success: errors.length === 0,
      records_processed: customers.length,
      records_success: success,
      records_error: errors.length,
      records_skipped: 0,
      errors: errors,
    };
  };

  const handleSyncProductsInternal = async (): Promise<SyncResult> => {
    const { data: config, error } = await supabase
      .from('woocommerce_config')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (!config || error) {
      throw new Error('WooCommerce configuration not found');
    }

    const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version || 'v3'}`;
    const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

    const response = await fetch(`${baseUrl}/products?per_page=${batchSize}`, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Products API Error: ${response.status} - ${response.statusText}`);
    }

    const products = await response.json();
    let success = 0;
    let errors: string[] = [];

    for (const product of products) {
      try {
        const { error: insertError } = await supabase
          .from('woocommerce_products_staging')
          .upsert({
            wc_product_id: product.id,
            organization_id: organizationId,
            name: product.name,
            sku: product.sku,
            price: parseFloat(product.price || '0'),
            currency: 'ZAR',
            status: product.status,
            stock_quantity: product.stock_quantity,
            raw_data: product,
            sync_status: 'pending',
            updated_at: new Date().toISOString(),
          }, {
            onConflict: 'organization_id,wc_product_id'
          });

        if (!insertError) success++;
        else errors.push(`Product ${product.id}: ${insertError.message}`);
      } catch (err) {
        errors.push(`Product ${product.id}: ${err}`);
      }
    }

    return {
      success: errors.length === 0,
      records_processed: products.length,
      records_success: success,
      records_error: errors.length,
      records_skipped: 0,
      errors: errors,
    };
  };

  const handleClearStaging = async (type?: 'customers' | 'products') => {
    if (confirm(`Are you sure you want to clear ${type || 'all'} staging data?`)) {
      setIsLoading(true);
      try {
        if (type === 'customers' || !type) {
          await supabase
            .from('woocommerce_customers_staging')
            .delete()
            .eq('organization_id', organizationId);
        }
        if (type === 'products' || !type) {
          await supabase
            .from('woocommerce_products_staging')
            .delete()
            .eq('organization_id', organizationId);
        }
        await loadStats();
      } catch (error) {
        console.error('Failed to clear staging:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const loadStats = async () => {
    try {
      // Load actual staging stats from database
      const [customersResult, productsResult] = await Promise.all([
        supabase
          .from('woocommerce_customers_staging')
          .select('sync_status')
          .eq('organization_id', organizationId),
        supabase
          .from('woocommerce_products_staging')
          .select('sync_status')
          .eq('organization_id', organizationId)
      ]);

      const customerStats = calculateStatsFromData(customersResult.data || []);
      const productStats = calculateStatsFromData(productsResult.data || []);

      setStats({
        customers: customerStats,
        products: productStats,
        lastSync: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
      // Set mock stats on error
      setStats({
        customers: { total: 0, pending: 0, synced: 0, error: 0 },
        products: { total: 0, pending: 0, synced: 0, error: 0 },
        lastSync: null
      });
    }
  };

  const calculateStatsFromData = (data: any[]) => {
    const total = data.length;
    const pending = data.filter(item => item.sync_status === 'pending').length;
    const synced = data.filter(item => item.sync_status === 'synced').length;
    const error = data.filter(item => item.sync_status === 'error').length;

    return { total, pending, synced, error };
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'error':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Error</Badge>;
      default:
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Unknown</Badge>;
    }
  };

  const calculateProgress = (synced: number, total: number) => {
    return total > 0 ? (synced / total) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">WooCommerce Staging Area</h1>
          <p className="text-muted-foreground">
            Real-time data synchronization and validation
          </p>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(connectionStatus)}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={initializeAndLoadStats}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Connection Status Alert */}
      {connectionStatus === 'error' && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Unable to connect to WooCommerce API. Please check your configuration settings.
          </AlertDescription>
        </Alert>
      )}

      {/* Sync Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Synchronization Controls
          </CardTitle>
          <CardDescription>
            Pull data from WooCommerce in batches of {batchSize} records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button 
              onClick={handleSyncCustomers}
              disabled={syncInProgress || connectionStatus !== 'connected'}
              className="flex items-center gap-2"
            >
              <Users className="w-4 h-4" />
              Sync Customers
              {syncInProgress && <RefreshCw className="w-4 h-4 animate-spin" />}
            </Button>
            
            <Button 
              onClick={handleSyncProducts}
              disabled={syncInProgress || connectionStatus !== 'connected'}
              className="flex items-center gap-2"
            >
              <Package className="w-4 h-4" />
              Sync Products
              {syncInProgress && <RefreshCw className="w-4 h-4 animate-spin" />}
            </Button>
            
            <Button 
              onClick={handleFullSync}
              disabled={syncInProgress || connectionStatus !== 'connected'}
              variant="default"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Full Download
              {syncInProgress && <RefreshCw className="w-4 h-4 animate-spin" />}
            </Button>

            <div className="flex items-center gap-2 ml-auto">
              <label className="text-sm font-medium">Batch Size:</label>
              <select 
                value={batchSize} 
                onChange={(e) => setBatchSize(Number(e.target.value))}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={200}>200</option>
                <option value={500}>500</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Staging Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customers Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Customers Staging
              </CardTitle>
              <CardDescription>
                {stats.customers.total} total records
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Sync Progress</span>
                <span className="text-sm font-medium">
                  {stats.customers.synced}/{stats.customers.total}
                </span>
              </div>
              <Progress value={calculateProgress(stats.customers.synced, stats.customers.total)} />
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{stats.customers.pending}</div>
                  <div className="text-xs text-muted-foreground">Pending</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{stats.customers.synced}</div>
                  <div className="text-xs text-muted-foreground">Synced</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">{stats.customers.error}</div>
                  <div className="text-xs text-muted-foreground">Errors</div>
                </div>
              </div>

              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleClearStaging('customers')}
                className="w-full"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Clear Customer Staging
              </Button>
            </CardContent>
          </Card>

          {/* Products Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5" />
                Products Staging
              </CardTitle>
              <CardDescription>
                {stats.products.total} total records
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Sync Progress</span>
                <span className="text-sm font-medium">
                  {stats.products.synced}/{stats.products.total}
                </span>
              </div>
              <Progress value={calculateProgress(stats.products.synced, stats.products.total)} />
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-yellow-600">{stats.products.pending}</div>
                  <div className="text-xs text-muted-foreground">Pending</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{stats.products.synced}</div>
                  <div className="text-xs text-muted-foreground">Synced</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">{stats.products.error}</div>
                  <div className="text-xs text-muted-foreground">Errors</div>
                </div>
              </div>

              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleClearStaging('products')}
                className="w-full"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Clear Product Staging
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Last Sync Result */}
      {lastSyncResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {lastSyncResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500" />
              )}
              Last Sync Result
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{lastSyncResult.records_processed}</div>
                <div className="text-sm text-muted-foreground">Processed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{lastSyncResult.records_success}</div>
                <div className="text-sm text-muted-foreground">Success</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{lastSyncResult.records_error}</div>
                <div className="text-sm text-muted-foreground">Errors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{lastSyncResult.records_skipped}</div>
                <div className="text-sm text-muted-foreground">Skipped</div>
              </div>
            </div>

            {lastSyncResult.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-2">Sync Errors:</div>
                  <ul className="list-disc list-inside space-y-1">
                    {lastSyncResult.errors.slice(0, 5).map((error, index) => (
                      <li key={index} className="text-sm">{error}</li>
                    ))}
                    {lastSyncResult.errors.length > 5 && (
                      <li className="text-sm">... and {lastSyncResult.errors.length - 5} more errors</li>
                    )}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage staging area and sync operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button variant="outline" onClick={() => handleClearStaging()}>
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All Staging
            </Button>
            
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Configure API
            </Button>
            
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Staging Data
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default WooCommerceStagingDashboard;
