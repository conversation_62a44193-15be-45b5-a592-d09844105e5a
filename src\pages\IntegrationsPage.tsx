import React, { useState, useEffect, useMemo } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { typedSupabaseQuery } from '@/utils/supabase-helpers';
import { Loader2 } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import IntegrationCard from '@/components/tech-hub/integrations/IntegrationCard';
import { useUserRealtimeState } from '@/hooks/useRealtimeState'; // Import the new hook
import WooCommerceIntegration from '@/components/tech-hub/integrations/WooCommerceIntegration';
import OdooIntegration from '@/components/tech-hub/integrations/OdooIntegration';
import { WooCommerceStagingCard } from '@/components/tech-hub/integrations/WooCommerceStagingCard';
import { QuickAccessPanel } from '@/components/tech-hub/integrations/QuickAccessPanel';
import { Card } from '@/components/ui/card';

// Types based on the design document
export interface SupportedIntegration {
  id: string; // e.g., 'odoo', 'woocommerce'
  name: string; // e.g., "Odoo ERP", "WooCommerce Store"
  icon: string; // Placeholder for icon path or component name
  description: string;
  detailPageRoute: string; // e.g., '/tech-hub/integrations/odoo'
}

export interface ConfiguredIntegration {
  id: string; // DB record ID from integration_configs
  integration_type: string; // 'odoo', 'woocommerce', etc. - matches SupportedIntegration.id
  user_id: string;
  config: Record<string, any>; // Non-sensitive config details
  is_globally_enabled?: boolean; // Optional as per design, ensure DB schema matches
  last_known_status?: 'connected' | 'error' | 'unknown' | 'disabled' | 'pending';
  last_status_check_at?: string;
  last_successful_sync_at?: string;
  created_at: string;
  updated_at: string;
}

// Combined type for display
export interface DisplayIntegration extends SupportedIntegration {
  configuredData?: ConfiguredIntegration;
  status: 'connected' | 'error' | 'unknown' | 'disabled' | 'pending' | 'not_configured'; // Made status non-optional
  isLoadingStatus?: boolean; // To show loading on individual card status
  lastStatusCheckAt?: string; // From live check or DB
}

// Placeholder for supported integrations - this could come from a config file or API
const SUPPORTED_INTEGRATIONS_LIST: SupportedIntegration[] = [
  {
    id: 'odoo',
    name: 'Odoo ERP',
    icon: 'OdooIcon', // Placeholder
    description: 'Synchronizes product catalog, orders, and customer data with your Odoo instance.',
    detailPageRoute: '/dashboard/tech-hub/integrations/odoo',
  },
  {
    id: 'woocommerce',
    name: 'WooCommerce Store',
    icon: 'WooCommerceIcon', // Placeholder
    description: 'Connect your e-commerce store for seamless data synchronization with staging area.',
    detailPageRoute: '/dashboard/tech-hub/integrations/woocommerce',
  },
  {
    id: 'woocommerce-staging',
    name: 'WooCommerce Staging',
    icon: 'DatabaseIcon', // Placeholder
    description: 'Real-time data synchronization staging area for WooCommerce validation.',
    detailPageRoute: '/dashboard/tech-hub/integrations/woocommerce/staging',
  },
  {
    id: 'woocommerce-config',
    name: 'WooCommerce Configuration',
    icon: 'SettingsIcon', // Placeholder
    description: 'Configure WooCommerce API connection and sync settings.',
    detailPageRoute: '/dashboard/tech-hub/integrations/woocommerce/configuration',
  },
  // Add other planned integrations here, e.g., Salesforce, QuickBooks
  // {
  //   id: 'salesforce',
  //   name: 'Salesforce CRM',
  //   icon: 'SalesforceIcon',
  //   description: 'Integrate with Salesforce CRM for customer data management.',
  //   detailPageRoute: '/tech-hub/integrations/salesforce', (To be created)
  // },
];

const IntegrationsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Use useUserRealtimeState to fetch and subscribe to configured integrations
  const [configuredIntegrations, loadingConfigs, errorConfigs, refetchConfigs] = useUserRealtimeState<ConfiguredIntegration>('integration_configs');

  // State for live statuses (might still need local state for temporary status during checks)
  const [liveStatuses, setLiveStatuses] = useState<Record<string, { status: DisplayIntegration['status'], last_status_check_at?: string }>>({});
  const [isPerformingHealthCheck, setIsPerformingHealthCheck] = useState<Record<string, boolean>>({});


  const performHealthCheck = async (integrationConfig: ConfiguredIntegration, isInitialLoad: boolean = false) => {
    if (!isInitialLoad) {
       setIsPerformingHealthCheck(prev => ({ ...prev, [integrationConfig.integration_type]: true }));
       setLiveStatuses(prev => ({
        ...prev,
        [integrationConfig.integration_type]: { status: 'pending', last_status_check_at: prev[integrationConfig.integration_type]?.last_status_check_at }
      }));
    }

    const checkTimestamp = new Date().toISOString();
    let newStatus: DisplayIntegration['status'] = 'error'; // Default to error

    try {
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session) {
        throw new Error(sessionError?.message || "Authentication session not found.");
      }
      const token = session.access_token;

      const gatewayUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/api-gateway/${integrationConfig.integration_type}/health`;
      
      const fetchOptions: RequestInit = {
        method: integrationConfig.integration_type === 'odoo' ? 'POST' : 'GET', // Odoo health is POST, Woo is GET
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json', // Odoo expects JSON, harmless for GET
        },
        // No body needed for these health checks from frontend to gateway; gateway constructs Odoo payload.
      };

      const response = await fetch(gatewayUrl, fetchOptions);

      if (response.ok) {
        // For Odoo, a successful response (even if it's an Odoo error in the JSON-RPC result) means the gateway reached Odoo.
        // We're checking basic connectivity and auth to the service.
        // A 200 from the gateway for the health endpoint implies the service is reachable.
        // More specific error parsing from the response body could be done if needed.
        // Example: const responseData = await response.json(); if (responseData.error) newStatus = 'error'; else newStatus = 'connected';
        newStatus = 'connected';
        if (!isInitialLoad) {
          toast.success(`Health check for ${integrationConfig.integration_type} successful.`);
        }
      } else {
        // Gateway itself or network error, or non-2xx from the backend service proxied by gateway
        const errorData = await response.json().catch(() => ({ message: response.statusText }));
        console.error(`Health check failed for ${integrationConfig.integration_type}: ${response.status}`, errorData);
        newStatus = 'error';
        if (!isInitialLoad) {
          toast.error(`Health check for ${integrationConfig.integration_type} failed: ${errorData.error || errorData.message || response.statusText}`);
        }
      }
    } catch (err: any) {
      console.error(`Error during health check for ${integrationConfig.integration_type}:`, err);
      newStatus = 'error';
      if (!isInitialLoad) {
        toast.error(`Health check for ${integrationConfig.integration_type} failed: ${err.message}`);
      }
    } finally {
      // Update Supabase and local state regardless of try/catch outcome, using the determined newStatus
      try {
        await typedSupabaseQuery('integration_configs')
          .update({
            last_known_status: newStatus,
            last_status_check_at: checkTimestamp,
          })
          .eq('id', integrationConfig.id);
        
        setLiveStatuses(prev => ({
          ...prev,
          [integrationConfig.integration_type]: { status: newStatus, last_status_check_at: checkTimestamp }
        }));
      } catch (dbError: any) {
        console.error(`Failed to update Supabase after health check for ${integrationConfig.integration_type}:`, dbError);
        // If Supabase update fails, local state might be out of sync.
        // Optionally, revert local state or show another error.
        // For now, we keep the locally determined status but log the DB error.
        if (!isInitialLoad) {
            toast.error(`Failed to save status for ${integrationConfig.integration_type}: ${dbError.message}`);
        }
      } finally {
         if (!isInitialLoad) {
            setIsPerformingHealthCheck(prev => ({ ...prev, [integrationConfig.integration_type]: false }));
         }
      }
    }
  };

  // Perform initial health checks after configurations are loaded
  useEffect(() => {
    if (configuredIntegrations.length > 0) {
      configuredIntegrations.forEach(config => {
         if (config.is_globally_enabled !== false) { // if enabled or undefined
           performHealthCheck(config, true); // true for initial load
         } else {
           // For disabled integrations, set their live status from DB or mark as 'disabled'
           setLiveStatuses(prev => ({
             ...prev,
             [config.integration_type]: {
               status: 'disabled',
               last_status_check_at: config.last_status_check_at || new Date().toISOString()
             }
           }));
         }
      });
    }
  }, [configuredIntegrations]); // Rerun when configuredIntegrations changes

  
  // Placeholder action handlers
  const handleToggleEnable = async (integrationId: string, currentConfigId: string | undefined, newEnabledState: boolean) => {
    toast.info(`Toggling ${integrationId} (${currentConfigId}) to ${newEnabledState ? 'enabled' : 'disabled'}`);
    if (!currentConfigId) {
      toast.error("Cannot toggle status for an unconfigured integration.");
      return;
    }
    // API call to update is_globally_enabled in integration_configs
    try {
      const { error: updateError } = await typedSupabaseQuery('integration_configs')
        .update({ is_globally_enabled: newEnabledState, updated_at: new Date().toISOString() })
        .eq('id', currentConfigId)
        .eq('integration_type', integrationId); // Ensure we're updating the correct type

      if (updateError) throw updateError;
      toast.success(`${integrationId} ${newEnabledState ? 'enabled' : 'disabled'} successfully.`);
      // Refetch data - useRealtimeState will update automatically, but refetch ensures consistency
      refetchConfigs();
    } catch (err: any) {
      toast.error(`Failed to toggle ${integrationId}: ${err.message}`);
    }
  };

  const handleTestConnection = async (integrationId: string, currentConfigId: string | undefined) => {
    toast.info(`Testing connection for ${integrationId} (${currentConfigId})...`);
    if (!currentConfigId) {
      toast.error("Cannot test connection for an unconfigured integration.");
      return;
    }
    // API call to api-gateway/{integrationId}/test_connection or health_check
    const targetIntegrationConfig = configuredIntegrations.find(
      (conf) => conf.integration_type === integrationId && conf.id === currentConfigId
    );
    if (!targetIntegrationConfig) {
      toast.error("Configuration not found for testing connection.");
      return;
    }
    await performHealthCheck(targetIntegrationConfig, false); // false for manual test
  };

  const handleManageSettings = (route: string) => {
    navigate(route);
  };

  // Compose the display list
  const displayIntegrations: DisplayIntegration[] = useMemo(() => {
    return SUPPORTED_INTEGRATIONS_LIST.map((integration) => {
      const configuredData = configuredIntegrations.find(
        (c) => c.integration_type === integration.id
      );
      const liveStatus = liveStatuses[integration.id];
      return {
        ...integration,
        configuredData,
        status: liveStatus?.status || configuredData?.last_known_status || 'not_configured',
        isLoadingStatus: isPerformingHealthCheck[integration.id] || false,
        lastStatusCheckAt: liveStatus?.last_status_check_at || configuredData?.last_status_check_at,
      };
    });
  }, [configuredIntegrations, liveStatuses, isPerformingHealthCheck]);

  // Check if we're on a specific integration page (after all hooks)
  const currentPath = location.pathname;
  const isOdooPage = currentPath.includes('/integrations/odoo');
  const isWooCommercePage = currentPath.includes('/integrations/woocommerce');

  // If we're on a specific integration page, render that component
  if (isOdooPage) {
    return <OdooIntegration />;
  }

  if (isWooCommercePage) {
    return <WooCommerceIntegration />;
  }

  // Render main integrations page
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold mb-4">Integrations</h1>

      {/* WooCommerce Staging System Quick Access */}
      <QuickAccessPanel />

      {/* WooCommerce Staging System Highlight */}
      <WooCommerceStagingCard
        connectionStatus={displayIntegrations.find(i => i.id === 'woocommerce')?.status}
        stagingRecords={{ customers: 0, products: 0 }}
      />
      {loadingConfigs ? (
        <div className="flex items-center justify-center h-32">
          <Loader2 className="animate-spin h-8 w-8 text-muted-foreground" />
          <span className="ml-3 text-muted-foreground">Loading integrations...</span>
        </div>
      ) : errorConfigs ? (
        <div className="text-red-600">
          <div>Error loading integrations. Please try again later.</div>
          {typeof errorConfigs === 'string' && <div className="text-xs mt-2">{errorConfigs}</div>}
          {typeof errorConfigs === 'object' && errorConfigs !== null && (
            <div className="text-xs mt-2">
              {'message' in errorConfigs && typeof errorConfigs.message === 'string'
                ? errorConfigs.message
                : JSON.stringify(errorConfigs)}
            </div>
          )}
        </div>
      ) : displayIntegrations.length === 0 ? (
        <div className="text-muted-foreground">No integrations available.</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayIntegrations.map((integration) => (
            <IntegrationCard
              key={integration.id}
              integration={integration}
              onToggleEnable={(newEnabledState) => handleToggleEnable(
                integration.id,
                integration.configuredData?.id,
                newEnabledState
              )}
              onTestConnection={handleTestConnection}
              onManageSettings={navigate}
              isPerformingHealthCheck={integration.isLoadingStatus}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default IntegrationsPage;
