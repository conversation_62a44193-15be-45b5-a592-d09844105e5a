/**
 * Quick Setup Component for WooCommerce API Testing
 * Allows rapid configuration and testing of real WooCommerce API
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Save,
  Globe,
  Key,
  Database
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

export function QuickSetup() {
  const [config, setConfig] = useState({
    store_url: '',
    consumer_key: '',
    consumer_secret: '',
    api_version: 'v3'
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; data?: any } | null>(null);
  const [saveResult, setSaveResult] = useState<string | null>(null);

  const [organizationId, setOrganizationId] = useState<string>('');

  useEffect(() => {
    initializeUser();
  }, []);

  const initializeUser = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (user && !error) {
        setOrganizationId(user.id);
      }
    } catch (error) {
      console.error('Failed to get user:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    setTestResult(null);
    setSaveResult(null);
  };

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      if (!config.store_url || !config.consumer_key || !config.consumer_secret) {
        throw new Error('Please fill in all required fields');
      }

      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      console.log('Testing connection to:', baseUrl);
      console.log('Using auth:', config.consumer_key.substring(0, 10) + '...');

      // Test system status endpoint
      const response = await fetch(`${baseUrl}/system_status`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult({ 
          success: true, 
          message: `✅ Connected successfully! WooCommerce ${data.version || 'API'} is accessible.`,
          data: data
        });
      } else {
        const errorText = await response.text();
        setTestResult({ 
          success: false, 
          message: `❌ Connection failed: HTTP ${response.status} - ${response.statusText}. ${errorText}` 
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testCustomersEndpoint = async () => {
    setIsLoading(true);
    
    try {
      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      const response = await fetch(`${baseUrl}/customers?per_page=5`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const customers = await response.json();
        setTestResult({ 
          success: true, 
          message: `✅ Customers API working! Found ${customers.length} customers.`,
          data: customers
        });
      } else {
        const errorText = await response.text();
        setTestResult({ 
          success: false, 
          message: `❌ Customers API failed: HTTP ${response.status} - ${errorText}` 
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `❌ Customers test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testProductsEndpoint = async () => {
    setIsLoading(true);
    
    try {
      const baseUrl = `${config.store_url}/wp-json/wc/${config.api_version}`;
      const auth = btoa(`${config.consumer_key}:${config.consumer_secret}`);

      const response = await fetch(`${baseUrl}/products?per_page=5`, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const products = await response.json();
        setTestResult({ 
          success: true, 
          message: `✅ Products API working! Found ${products.length} products.`,
          data: products
        });
      } else {
        const errorText = await response.text();
        setTestResult({ 
          success: false, 
          message: `❌ Products API failed: HTTP ${response.status} - ${errorText}` 
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `❌ Products test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfiguration = async () => {
    setIsLoading(true);
    setSaveResult(null);

    try {
      if (!organizationId) {
        throw new Error('User not authenticated');
      }

      // Save to woocommerce_config table
      const { error: configError } = await supabase
        .from('woocommerce_config')
        .upsert({
          organization_id: organizationId,
          store_url: config.store_url,
          consumer_key: config.consumer_key,
          consumer_secret: config.consumer_secret,
          api_version: config.api_version,
          sync_enabled: true,
          batch_size: 100,
          sync_frequency_minutes: 15,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'organization_id'
        });

      if (configError) {
        throw configError;
      }

      // Also save to integration_configs for the main integrations system
      const { error: integrationError } = await supabase
        .from('integration_configs')
        .upsert({
          user_id: organizationId,
          integration_type: 'woocommerce',
          config: {
            store_url: config.store_url,
            api_version: config.api_version,
            sync_enabled: true,
            batch_size: 100,
            sync_frequency_minutes: 15,
          },
          is_globally_enabled: true,
          last_known_status: 'unknown',
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id,integration_type'
        });

      if (integrationError) {
        console.warn('Failed to update integration_configs:', integrationError);
      }

      setSaveResult(`✅ Configuration saved successfully! You can now use the staging dashboard and integrations.`);
    } catch (error) {
      setSaveResult(`❌ Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="w-5 h-5" />
          WooCommerce API Quick Setup & Testing
        </CardTitle>
        <CardDescription>
          Configure and test your WooCommerce API connection with real data
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Configuration Form */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="store_url">Store URL</Label>
            <Input
              id="store_url"
              placeholder="https://yourstore.com"
              value={config.store_url}
              onChange={(e) => handleInputChange('store_url', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="api_version">API Version</Label>
            <select
              id="api_version"
              value={config.api_version}
              onChange={(e) => handleInputChange('api_version', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="v3">v3 (Recommended)</option>
              <option value="v2">v2</option>
              <option value="v1">v1</option>
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="consumer_key">Consumer Key</Label>
            <Input
              id="consumer_key"
              placeholder="ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              value={config.consumer_key}
              onChange={(e) => handleInputChange('consumer_key', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="consumer_secret">Consumer Secret</Label>
            <Input
              id="consumer_secret"
              type="password"
              placeholder="cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
              value={config.consumer_secret}
              onChange={(e) => handleInputChange('consumer_secret', e.target.value)}
            />
          </div>
        </div>

        {/* Test Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={testConnection}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <TestTube className="w-4 h-4" />
            Test Connection
          </Button>

          <Button
            onClick={testCustomersEndpoint}
            disabled={isLoading}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Database className="w-4 h-4" />
            Test Customers API
          </Button>

          <Button
            onClick={testProductsEndpoint}
            disabled={isLoading}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Database className="w-4 h-4" />
            Test Products API
          </Button>

          <Button
            onClick={saveConfiguration}
            disabled={isLoading || !testResult?.success}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            Save Configuration
          </Button>
        </div>

        {/* Test Results */}
        {testResult && (
          <Alert className={testResult.success ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}>
            {testResult.success ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription className={testResult.success ? 'text-green-800' : 'text-red-800'}>
              {testResult.message}
              {testResult.data && (
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">View API Response</summary>
                  <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(testResult.data, null, 2)}
                  </pre>
                </details>
              )}
            </AlertDescription>
          </Alert>
        )}

        {/* Save Results */}
        {saveResult && (
          <Alert className={saveResult.includes('✅') ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}>
            <AlertDescription>
              {saveResult}
            </AlertDescription>
          </Alert>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Key className="w-4 h-4" />
            Quick Setup Instructions:
          </h4>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>Enter your WooCommerce store URL (e.g., https://yourstore.com)</li>
            <li>Go to WooCommerce → Settings → Advanced → REST API</li>
            <li>Click "Add Key" and set permissions to "Read/Write"</li>
            <li>Copy the Consumer Key and Consumer Secret here</li>
            <li>Test the connection to verify it works</li>
            <li>Save the configuration to use in staging dashboard</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
}
